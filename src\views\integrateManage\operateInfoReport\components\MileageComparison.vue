<template>
  <div class="comparison-container">
    <el-row :gutter="10">
      <el-col>
        <SubTitle title="各组行驶里程对比"></SubTitle>
      </el-col>
      <el-col>
        <TimeScreen class="mt-16"></TimeScreen>
      </el-col>
    </el-row>
    <div class="chart-box">
      <div class="chart" id="comparison-chart"></div>
    </div>
  </div>
</template>

<script>
  import SubTitle from "./SubTitle.vue";
  import TimeScreen from "./TimeScreen.vue";
  import * as echarts from "echarts";

  export default {
    components: {
      SubTitle,
      TimeScreen,
    },
    data() {
      return {
        chart: null,
        chartData: [
          { name: "大型床位", value: 340, color: "#008000" },
          { name: "小型床位", value: 240, color: "#ffa500" },
          { name: "小诊所组", value: 180, color: "#800080" },
        ],
      };
    },
    mounted() {
      this.initChart();
    },
    beforeDestroy() {
      if (this.chart) {
        this.chart.dispose();
      }
    },
    methods: {
      initChart() {
        const chartDom = document.getElementById("comparison-chart");
        this.chart = echarts.init(chartDom);

        const option = {
          grid: {
            left: "3%",
            right: "4%",
            bottom: "3%",
            containLabel: true,
          },
          xAxis: {
            type: "category",
            data: this.chartData.map((item) => item.name),
            axisLine: {
              lineStyle: {
                color: "#e0e0e0",
              },
            },
            axisTick: {
              show: false,
            },
            axisLabel: {
              color: "#666",
              fontSize: 12,
              margin: 15,
            },
          },
          yAxis: {
            type: "value",
            name: "里程 (km)",
            nameTextStyle: {
              color: "#666",
              fontSize: 12,
            },
            axisLine: {
              show: false,
            },
            axisTick: {
              show: false,
            },
            axisLabel: {
              color: "#666",
              fontSize: 12,
            },
            splitLine: {
              lineStyle: {
                color: "#f0f0f0",
                type: "solid",
              },
            },
            max: 400,
          },
          series: [
            {
              type: "bar",
              data: this.chartData.map((item) => ({
                name: item.name,
                value: item.value,
                itemStyle: {
                  color: item.color,
                  borderRadius: [4, 4, 0, 0],
                },
              })),
              barWidth: "40%",
              label: {
                show: false,
              },
            },
          ],
          // legend: {
          //   show: true,
          //   bottom: "5%",
          //   left: "center",
          //   itemWidth: 12,
          //   itemHeight: 12,
          //   textStyle: {
          //     color: "#666",
          //     fontSize: 12,
          //   },
          //   data: this.chartData.map((item) => ({
          //     name: item.name,
          //     icon: "circle",
          //     textStyle: {
          //       color: "#666",
          //     },
          //   })),
          // },
          legend: {
            data: ["大型床位", "小型床位", "小诊所组"],
            textStyle: {
              color: "#fff",
            },
          },
          tooltip: {
            trigger: "axis",
            axisPointer: {
              type: "shadow",
            },
            formatter: function (params) {
              const data = params[0];
              return `${data.name}<br/>${data.value} km`;
            },
          },
        };

        this.chart.setOption(option);
      },
    },
  };
</script>

<style lang="scss" scoped>
  .comparison-container {
    background-color: #fff;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
    padding: 16px;
    border-radius: 12px;
  }
  .chart-box {
    width: 100%;
    height: 400px;
    .chart {
      width: 100%;
      height: 100%;
    }
  }
</style>
